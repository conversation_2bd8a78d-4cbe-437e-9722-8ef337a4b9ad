请确认自己的开发板硬件版本，如果硬件版本，在配置中进行ev_board type进行选择
1.4与1.5只有io进行变更
可以查看官方文档，确认具体细节https://docs.espressif.com/projects/esp-dev-kits/en/latest/esp32s3/esp32-s3-lcd-ev-board/user_guide.html
具体调整为：
I2C_SCL     IO18    ->     IO48
I2C_SDA     IO8     ->     IO47
LCD_DATA6   IO47    ->     IO8
LCD_DATA7   IO48    ->     IO18


## 支持的屏幕分辨率

本版本现在支持两种屏幕分辨率：

### 480x480 (默认)
- 分辨率: 480x480 像素
- 像素时钟: 16 MHz
- 适用于方形显示屏

### 800x480 (宽屏)
- 分辨率: 800x480 像素
- 像素时钟: 33 MHz
- 适用于宽屏显示器

## 构建配置

开发板支持多种构建配置：

- `esp-s3-lcd-ev-board-1p4-480x480`: V1.4 开发板配 480x480 显示屏
- `esp-s3-lcd-ev-board-1p5-480x480`: V1.5 开发板配 480x480 显示屏
- `esp-s3-lcd-ev-board-1p4-800x480`: V1.4 开发板配 800x480 显示屏
- `esp-s3-lcd-ev-board-1p5-800x480`: V1.5 开发板配 800x480 显示屏

## 配置选项

使用 `idf.py menuconfig` 时，可以选择：

1. **开发板版本**: 选择 V1.4 或 V1.5 硬件版本
2. **LCD 类型**: 选择 480x480 或 800x480 分辨率