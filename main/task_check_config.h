#ifndef TASK_CHECK_CONFIG_H
#define TASK_CHECK_CONFIG_H

// 任务检查配置
// 可以通过Settings类动态配置，也可以在这里设置默认值

// 默认任务检查URL
#define DEFAULT_TASK_CHECK_URL "http://rdmytikasapitest.lezhilong.cn/intermediary/api/check_task"

// 任务检查间隔（毫秒）
#define TASK_CHECK_INTERVAL_MS 15000

// 任务检查超时时间（毫秒）
#define TASK_CHECK_TIMEOUT_MS 10000

// 任务检查重试次数
#define TASK_CHECK_MAX_RETRIES 3

// 任务检查重试间隔（毫秒）
#define TASK_CHECK_RETRY_INTERVAL_MS 1000

// 是否在检测到任务时发送custom消息到服务器
#define TASK_CHECK_SEND_CUSTOM_MESSAGE 1

#endif // TASK_CHECK_CONFIG_H 