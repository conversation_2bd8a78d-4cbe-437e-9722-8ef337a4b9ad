# 任务检查功能使用示例

## 完整测试流程

### 1. 环境准备

确保你的开发环境已经配置好：
- ESP-IDF开发环境
- Python 3.7+
- 网络连接

### 2. 启动测试服务器

#### 启动任务检查HTTP服务器
```bash
cd scripts
python3 test_task_check.py 8080
```

#### 启动WebSocket服务器（用于接收custom消息）
```bash
cd scripts
python3 test_custom_message.py 8081
```

### 3. 配置ESP32设备

#### 方法1：使用默认配置
设备会自动使用默认的任务检查URL。

#### 方法2：动态配置
在代码中添加配置：
```cpp
#include "settings.h"

// 配置任务检查URL
Settings settings("task_check", true);
settings.SetString("url", "http://localhost:8080/api/device/task");
```

### 4. 编译和烧录

```bash
idf.py build
idf.py flash monitor
```

### 5. 观察日志输出

#### ESP32设备日志
```
I (1234) Application: No task available for device wifi
I (6234) Application: Device task received: {"success":true,"has_task":true,"task":"请检查设备状态并重启系统"}
I (6234) Application: Sending custom message to server: {"type":"custom","payload":{"task_content":"请检查设备状态并重启系统"}}
```

#### 任务检查服务器日志
```
收到来自设备 wifi 的任务检查请求
Device-Id: aa:bb:cc:dd:ee:ff
Client-Id: device-uuid-123
请求体: {'device_name': 'wifi'}
响应: {"success":true,"has_task":true,"task":"请检查设备状态并重启系统"}
```

#### WebSocket服务器日志
```
收到消息类型: hello
收到消息类型: custom
任务内容: 请检查设备状态并重启系统
开始处理任务: 请检查设备状态并重启系统
执行检查任务
执行重启任务
任务处理完成
```

## 实际应用场景

### 1. 语音播报任务

服务器返回任务：
```json
{
    "success": true,
    "has_task": true,
    "task": "请播报当前设备状态",
    "timestamp": "2024-01-01T12:00:00Z",
    "message": "设备 wifi 有新任务"
}
```

ESP32发送custom消息：
```json
{
    "type": "custom",
    "payload": {
        "task_content": "请播报当前设备状态"
    }
}
```

### 2. 设备控制任务

服务器返回任务：
```json
{
    "success": true,
    "has_task": true,
    "task": "请打开红色LED指示灯",
    "timestamp": "2024-01-01T12:00:00Z",
    "message": "设备 wifi 有新任务"
}
```

ESP32发送custom消息：
```json
{
    "type": "custom",
    "payload": {
        "task_content": "请打开红色LED指示灯"
    }
}
```

### 3. 系统重启任务

服务器返回任务：
```json
{
    "success": true,
    "has_task": true,
    "task": "请执行系统重启",
    "timestamp": "2024-01-01T12:00:00Z",
    "message": "设备 wifi 有新任务"
}
```

ESP32发送custom消息：
```json
{
    "type": "custom",
    "payload": {
        "task_content": "请执行系统重启"
    }
}
```

## 故障排除

### 1. 任务检查失败

**问题**: 日志显示 "Task check failed"
**解决方案**:
- 检查任务检查服务器是否正常运行
- 确认URL配置正确
- 检查网络连接

### 2. Custom消息发送失败

**问题**: 日志显示 "Cannot send custom message"
**解决方案**:
- 确保音频通道已打开
- 检查协议连接状态
- 确认WebSocket服务器正在运行

### 3. 消息格式错误

**问题**: 服务器无法解析custom消息
**解决方案**:
- 检查JSON格式是否正确
- 确认payload字段包含完整的任务信息
- 验证消息类型为"custom"

## 性能优化建议

### 1. 调整检查频率
根据实际需求调整任务检查间隔：
```c
#define TASK_CHECK_INTERVAL_MS 10000  // 10秒
```

### 2. 优化网络请求
- 使用HTTPS协议提高安全性
- 添加请求超时设置
- 实现连接池管理

### 3. 内存管理
- 及时释放JSON对象
- 避免内存泄漏
- 监控内存使用情况

## 扩展功能

### 1. 任务优先级处理
可以根据任务优先级调整处理顺序：
```cpp
int priority = payload.get('priority', 1);
if (priority > 5) {
    // 高优先级任务立即处理
} else {
    // 普通任务排队处理
}
```

### 2. 任务状态反馈
可以向服务器反馈任务执行状态：
```cpp
// 发送任务开始执行消息
SendCustomMessage("task_start", task_id);

// 发送任务完成消息
SendCustomMessage("task_complete", task_id);
```

### 3. 离线任务缓存
可以在离线时缓存任务，网络恢复后执行：
```cpp
// 保存任务到本地存储
SaveTaskToStorage(task_data);

// 网络恢复后执行缓存的任务
ExecuteCachedTasks();
``` 