# 设备任务检查功能

## 概述

设备任务检查功能允许ESP32设备定期向服务器查询是否有需要执行的任务。该功能每5秒检查一次，通过HTTP POST请求与服务器通信。

## 功能特性

- **定时检查**: 每5秒自动检查一次任务
- **重试机制**: 网络失败时自动重试，最多3次
- **配置灵活**: 支持通过Settings动态配置任务检查URL
- **错误处理**: 完善的错误处理和日志记录
- **任务处理**: 支持接收和处理服务器返回的任务

## 配置

### 1. 默认配置

在 `task_check_config.h` 中可以修改默认配置：

```c
// 默认任务检查URL
#define DEFAULT_TASK_CHECK_URL "http://localhost:8080/api/device/task"

// 任务检查间隔（毫秒）
#define TASK_CHECK_INTERVAL_MS 5000

// 任务检查超时时间（毫秒）
#define TASK_CHECK_TIMEOUT_MS 10000

// 任务检查重试次数
#define TASK_CHECK_MAX_RETRIES 3

// 任务检查重试间隔（毫秒）
#define TASK_CHECK_RETRY_INTERVAL_MS 1000
```

### 2. 动态配置

可以通过Settings类动态配置任务检查URL：

```cpp
Settings settings("task_check", true);
settings.SetString("url", "http://your-server.com/api/device/task");
```

## API接口

### 请求格式

设备向服务器发送POST请求，请求体格式：

```json
{
    "device_name": "设备类型名称"
}
```

请求头包含：
- `Content-Type: application/json`
- `Device-Id: 设备MAC地址`
- `Client-Id: 设备UUID`

### 响应格式

服务器返回JSON格式响应：

```json
{
    "success": true,
    "has_task": true,
    "task": {
        "task_id": "task_123",
        "task_type": "speak",
        "content": "Hello, this is a test message",
        "priority": 1
    },
    "timestamp": "2024-01-01T12:00:00Z",
    "message": "设备 wifi 有新任务"
}
```

字段说明：
- `success`: 请求是否成功
- `has_task`: 是否有任务需要执行
- `task`: 任务详情对象（当has_task为true时）
- `timestamp`: 服务器时间戳
- `message`: 响应消息

## 任务处理

当接收到任务时，系统会：

1. 记录任务日志
2. **向服务器发送custom类型的消息**，将任务内容作为payload传递
3. 在主事件循环中调度任务处理
4. 在显示屏上显示任务通知

### Custom消息格式

当检测到有任务时，设备会向服务器发送一个custom类型的消息：

```json
{
    "type": "custom",
    "payload": {
        "task_content": "请检查设备状态"
    }
}
```

**注意**: 接口返回的`task`字段是字符串类型，不是JSON对象。设备会将这个字符串作为`task_content`字段包含在custom消息的payload中。

这个消息会通过现有的协议（MQTT或WebSocket）发送到服务器，与发送"hello"消息的方式和格式相同。

### 配置选项

可以通过修改 `task_check_config.h` 中的配置来控制是否发送custom消息：

```c
// 是否在检测到任务时发送custom消息到服务器
#define TASK_CHECK_SEND_CUSTOM_MESSAGE 1  // 1: 启用, 0: 禁用
```

### 扩展任务处理

可以在 `CheckDeviceTask()` 方法中的任务处理部分添加具体的任务执行逻辑：

```cpp
Schedule([this, task_content = std::string(task->valuestring)]() {
    // 处理字符串类型的任务内容
    ESP_LOGI(TAG, "Processing task: %s", task_content.c_str());
    
    // 可以根据任务内容进行不同的处理
    if (task_content.find("检查") != std::string::npos) {
        // 处理检查类任务
        ESP_LOGI(TAG, "执行检查任务");
    } else if (task_content.find("重启") != std::string::npos) {
        // 处理重启类任务
        ESP_LOGI(TAG, "执行重启任务");
    } else {
        // 处理其他类型任务
        ESP_LOGI(TAG, "执行通用任务");
    }
    
    // 显示任务通知
    auto display = Board::GetInstance().GetDisplay();
    display->SetChatMessage("system", task_content.c_str());
});
```

## 任务检查机制

### 定时器管理

系统使用一次性定时器管理机制，确保任务检查的5秒间隔：

1. **初始启动**: 系统启动时启动一次性定时器，5秒后触发第一次检查
2. **处理中保护**: 当任务检查正在进行时，新的定时器事件会被忽略
3. **完成重启**: 任务检测和处理完成后，清除处理标志并重新启动定时器
4. **单一启动点**: 定时器只在 `RestartTaskCheckTimer` 方法中启动，避免重复启动

### 处理流程

```
启动定时器 → 5秒后触发 → 检查任务 → 处理任务 → 清除标志 → 重新启动定时器
     ↓           ↓           ↓           ↓           ↓           ↓
   一次性      OnTaskCheck   HTTP请求    发送消息    task_check_  5秒后
   定时器        Timer      解析响应    显示通知    in_progress_  再次触发
                                                      = false
```

### 状态管理

- `task_check_in_progress_`: 任务检查处理中标志
  - `true`: 正在处理任务，忽略新的定时器事件
  - `false`: 空闲状态，可以处理新的定时器事件

### 关键方法

- `OnTaskCheckTimer()`: 定时器回调，只负责设置事件位，不启动定时器
- `CheckDeviceTask()`: 执行任务检查和处理
- `RestartTaskCheckTimer()`: 清除处理标志并重新启动定时器

### 定时器类型

- **一次性定时器**: 使用 `esp_timer_start_once()` 确保精确的5秒间隔
- **单一启动点**: 只在 `RestartTaskCheckTimer` 中启动定时器，避免重复启动
- **状态保护**: 通过标志位防止并发处理

## 日志

系统会记录以下日志：

- `Task check failed`: 任务检查失败
- `Device task received`: 接收到设备任务
- `Sending custom message to server`: 发送custom消息到服务器
- `Cannot send custom message`: 无法发送custom消息（协议不可用或音频通道未打开）
- `No task available`: 没有可用任务

## 注意事项

1. 确保设备已连接到网络
2. 配置正确的任务检查URL
3. 服务器需要实现相应的API接口
4. 确保音频通道已打开才能发送custom消息
5. 建议在生产环境中使用HTTPS协议

## 示例服务器响应

### 有任务的情况

```json
{
    "success": true,
    "has_task": true,
    "task": "请检查设备状态",
    "timestamp": "2024-01-01T12:00:00Z",
    "message": "设备 wifi 有新任务"
}
```

### 无任务的情况

```json
{
    "success": true,
    "has_task": false,
    "task": null,
    "timestamp": "2024-01-01T12:00:00Z",
    "message": "设备 wifi 暂无任务"
}
```

## 新增方法

### Application::SendCustomMessage()

为了方便发送custom类型的消息，在Application类中添加了`SendCustomMessage`方法：

```cpp
void Application::SendCustomMessage(const std::string& payload);
```

这个方法会：
1. 在主事件循环中调度消息发送
2. 检查协议是否已初始化
3. **如果音频通道未打开，会自动尝试打开音频通道**
4. 通过Protocol类的SendCustomMessage方法发送消息
5. 记录发送日志

**注意**: 如果音频通道打开失败，消息将不会发送，并会记录错误日志。

### Protocol::SendCustomMessage()

在Protocol基类中添加了`SendCustomMessage`方法：

```cpp
virtual void Protocol::SendCustomMessage(const std::string& payload);
```

这个方法会构造标准的custom消息格式：
```json
{
    "session_id": "session_123",
    "type": "custom",
    "payload": { ... }
}
```