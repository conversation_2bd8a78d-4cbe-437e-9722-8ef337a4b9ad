# 任务检查功能使用说明

## 快速开始

### 1. 编译和烧录

1. 确保你的ESP32开发环境已经配置好
2. 编译项目：
   ```bash
   idf.py build
   ```
3. 烧录到设备：
   ```bash
   idf.py flash monitor
   ```

### 2. 配置任务检查URL

#### 方法1：使用默认配置
默认情况下，设备会使用 `http://localhost:8080/api/device/task` 作为任务检查URL。

#### 方法2：动态配置
可以通过代码动态配置URL：

```cpp
#include "settings.h"

// 配置任务检查URL
Settings settings("task_check", true);
settings.SetString("url", "http://your-server.com/api/device/task");
```

### 3. 启动测试服务器

使用提供的Python测试脚本：

```bash
cd scripts
python3 test_task_check.py
```

或者指定端口：

```bash
python3 test_task_check.py 9000
```

### 4. 测试Custom消息发送

要测试custom消息的发送功能，可以使用提供的WebSocket测试服务器：

```bash
cd scripts
python3 test_custom_message.py
```

这个测试服务器会：
1. 监听WebSocket连接
2. 接收并解析ESP32发送的消息
3. 特别处理custom类型的消息
4. 显示任务详情和处理结果

**测试流程**：
1. 启动WebSocket测试服务器
2. 启动任务检查测试服务器（test_task_check.py）
3. 编译并烧录ESP32固件
4. 观察两个服务器的日志输出
5. 验证custom消息的发送和接收

**预期结果**：
- 任务检查服务器会定期收到ESP32的HTTP请求
- WebSocket服务器会收到ESP32发送的custom消息
- 日志会显示任务详情和处理过程

### 5. 测试自动连接功能

要测试音频通道自动打开功能，可以使用专门的测试服务器：

```bash
cd scripts
python3 test_custom_message_auto_connect.py
```

这个测试服务器会：
1. 监控连接建立过程
2. 验证custom消息是否在正确的时机发送
3. 统计连接次数和消息数量
4. 提供测试总结报告

**测试场景**：
- 设备在音频通道未打开时收到任务
- 验证设备是否会自动建立连接
- 确认custom消息在连接建立后发送

**预期结果**：
- 设备会自动建立音频通道连接
- 先发送hello消息，再发送custom消息
- 测试总结显示连接成功建立

### 6. 日志分析

任务检查功能现在包含详细的日志输出，可以使用专门的日志分析脚本来分析执行情况：

```bash
cd scripts
python3 analyze_task_check_logs.py esp32_logs.txt
```

这个分析脚本会：
1. 解析ESP32的日志格式
2. 统计任务检查的成功率和失败率
3. 分析音频通道打开情况
4. 统计custom消息发送次数
5. 分析时间间隔和性能
6. 提供错误统计和示例

**分析报告包含**：
- 📊 基本统计（检查次数、成功率等）
- 📋 任务相关（收到任务数、发送消息数）
- 🔊 音频通道（打开次数、成功率）
- ❌ 错误统计（HTTP错误、JSON解析错误等）
- ⏱️ 时间分析（检查间隔、性能指标）
- 📝 关键日志示例

**使用方法**：
1. 运行ESP32设备并收集日志
2. 将日志保存到文件（如esp32_logs.txt）
3. 运行分析脚本
4. 查看分析报告，了解功能运行情况

### 4. 观察日志

在ESP32的串口监视器中，你应该能看到类似以下的日志：

```
I (1234) Application: No task available for device wifi
I (6234) Application: Device task received: {"success":true,"has_task":true,"task":{...}}
I (6234) Application: Processing task: {"task_id":"task_123",...}
```

## 功能验证

### 1. 正常检查（无任务）
当服务器返回无任务时，设备会显示：
```
I (xxxx) Application: No task available for device wifi
```

### 2. 接收到任务
当服务器返回任务时，设备会：
1. 记录任务日志
2. **向服务器发送custom类型的消息**，将任务内容作为payload传递
3. 在显示屏上显示通知
4. 在主事件循环中处理任务

### 3. Custom消息发送
当检测到有任务时，设备会自动向服务器发送一个custom类型的消息：

```json
{
    "type": "custom",
    "payload": {
        "task_content": "请检查设备状态"
    }
}
```

**注意**: 接口返回的`task`字段是字符串类型，设备会将这个字符串作为`task_content`字段包含在custom消息的payload中。

这个消息会通过现有的协议（MQTT或WebSocket）发送，与发送"hello"消息的方式和格式相同。

**注意**: SendCustomMessage方法会自动检查并打开音频通道。如果音频通道未打开，会先尝试建立连接，然后发送custom消息。只有在音频通道打开失败时才会显示警告信息。

### 3. 网络错误处理
当网络连接失败时，设备会：
1. 自动重试（最多3次）
2. 记录错误日志
3. 继续下一次定时检查

## 自定义任务处理

### 1. 扩展任务类型

在 `application.cc` 的 `CheckDeviceTask()` 方法中，可以添加自定义任务处理逻辑：

```cpp
Schedule([this, task_str = std::string(cJSON_PrintUnformatted(task))]() {
    cJSON* task_obj = cJSON_Parse(task_str.c_str());
    cJSON* task_type = cJSON_GetObjectItem(task_obj, "task_type");
    cJSON* content = cJSON_GetObjectItem(task_obj, "content");
    
    if (cJSON_IsString(task_type) && cJSON_IsString(content)) {
        std::string type = task_type->valuestring;
        std::string task_content = content->valuestring;
        
        if (type == "speak") {
            // 处理语音任务
            auto display = Board::GetInstance().GetDisplay();
            display->SetChatMessage("system", task_content.c_str());
        } else if (type == "led_control") {
            // 处理LED控制任务
            auto led = Board::GetInstance().GetLed();
            // 根据content内容控制LED
        } else if (type == "reboot") {
            // 处理重启任务
            Reboot();
        }
    }
    
    cJSON_Delete(task_obj);
});
```

### 2. 添加新的任务类型

1. 在服务器端定义新的任务类型
2. 在客户端添加相应的处理逻辑
3. 测试新任务类型的处理

## 配置选项

### 1. 修改检查间隔

在 `task_check_config.h` 中修改：

```c
#define TASK_CHECK_INTERVAL_MS 10000  // 改为10秒
```

### 2. 修改重试次数

```c
#define TASK_CHECK_MAX_RETRIES 5  // 改为5次重试
```

### 3. 修改重试间隔

```c
#define TASK_CHECK_RETRY_INTERVAL_MS 2000  // 改为2秒
```

## 故障排除

### 1. 网络连接问题

**症状**: 日志显示 "Failed to open HTTP connection"
**解决方案**:
- 检查设备网络连接
- 确认服务器地址正确
- 检查防火墙设置

### 2. 服务器响应错误

**症状**: 日志显示 "Task check failed, status code: xxx"
**解决方案**:
- 检查服务器是否正常运行
- 确认API接口正确
- 检查请求格式

### 3. JSON解析错误

**症状**: 日志显示 "Failed to parse task check response"
**解决方案**:
- 检查服务器返回的JSON格式
- 确认响应字段完整

### 4. 内存不足

**症状**: 设备重启或异常
**解决方案**:
- 减少任务检查频率
- 优化任务处理逻辑
- 检查内存使用情况

## 性能优化

### 1. 减少网络请求频率
如果不需要频繁检查，可以增加检查间隔：

```c
#define TASK_CHECK_INTERVAL_MS 30000  // 30秒
```

### 2. 优化任务处理
避免在任务处理中执行耗时操作，使用异步处理：

```cpp
Schedule([this, task_data]() {
    // 异步处理任务
    // 避免阻塞主线程
});
```

### 3. 内存管理
及时释放JSON对象，避免内存泄漏：

```cpp
cJSON* response = cJSON_Parse(response_data.c_str());
// 处理响应...
cJSON_Delete(response);  // 重要：释放内存
```

## 安全考虑

### 1. 使用HTTPS
在生产环境中，建议使用HTTPS协议：

```cpp
#define DEFAULT_TASK_CHECK_URL "https://your-server.com/api/device/task"
```

### 2. 添加认证
可以在请求头中添加认证信息：

```cpp
http->SetHeader("Authorization", "Bearer your-token");
```

### 3. 验证服务器证书
确保服务器使用有效的SSL证书。

## 扩展功能

### 1. 任务优先级
可以根据任务优先级调整处理顺序。

### 2. 任务队列
可以实现任务队列，支持多个任务的处理。

### 3. 任务状态反馈
可以向服务器反馈任务执行状态。

### 4. 离线任务缓存
可以在离线时缓存任务，网络恢复后执行。 