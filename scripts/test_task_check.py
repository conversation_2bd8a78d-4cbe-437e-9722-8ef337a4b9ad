#!/usr/bin/env python3
"""
任务检查服务器测试脚本

这个脚本模拟一个简单的HTTP服务器，用于测试ESP32设备的任务检查功能。
"""

import json
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading

class TaskCheckHandler(BaseHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        self.task_counter = 0
        super().__init__(*args, **kwargs)
    
    def do_POST(self):
        """处理POST请求"""
        try:
            # 读取请求体
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            
            # 解析JSON请求
            request_json = json.loads(post_data.decode('utf-8'))
            device_name = request_json.get('device_name', 'unknown')
            
            # 获取请求头信息
            device_id = self.headers.get('Device-Id', 'unknown')
            client_id = self.headers.get('Client-Id', 'unknown')
            
            print(f"收到来自设备 {device_name} 的任务检查请求")
            print(f"Device-Id: {device_id}")
            print(f"Client-Id: {client_id}")
            print(f"请求体: {request_json}")
            
            # 模拟任务生成逻辑
            # 每10次请求返回一次任务
            request_count = self.task_counter + 1 # 使用self.task_counter
            timestamp = time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime())
            
            if request_count % 10 == 0:
                response_data = {
                    'success': True,
                    'has_task': True,
                    'task': '请检查设备状态并重启系统',
                    'timestamp': timestamp,
                    'message': f'设备 {device_name} 有新任务'
                }
            else:
                response_data = {
                    'success': True,
                    'has_task': False,
                    'task': None,
                    'timestamp': timestamp,
                    'message': f'设备 {device_name} 暂无任务'
                }
            
            # 发送响应
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response_json = json.dumps(response_data, ensure_ascii=False)
            self.wfile.write(response_json.encode('utf-8'))
            
            print(f"响应: {response_json}")
            print("-" * 50)
            
        except Exception as e:
            print(f"处理请求时出错: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            error_response = {
                "success": False,
                "error": str(e),
                "message": "服务器内部错误"
            }
            self.wfile.write(json.dumps(error_response).encode('utf-8'))
    
    def do_OPTIONS(self):
        """处理CORS预检请求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Device-Id, Client-Id')
        self.end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def start_server(port=8080):
    """启动HTTP服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, TaskCheckHandler)
    print(f"任务检查服务器启动在端口 {port}")
    print(f"访问地址: http://localhost:{port}/api/device/task")
    print("按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n服务器停止")
        httpd.server_close()

if __name__ == '__main__':
    import sys
    
    # 解析命令行参数
    port = 8080
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("端口号必须是整数")
            sys.exit(1)
    
    start_server(port) 