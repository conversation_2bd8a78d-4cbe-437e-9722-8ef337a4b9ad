#!/usr/bin/env python3
"""
测试格式符的Python脚本
验证ESP32日志中使用的格式符是否正确
"""

def test_format_specifiers():
    """测试格式符"""
    print("=== 格式符测试 ===")
    
    # 模拟esp_timer_get_time()返回int64_t
    esp_timer_get_time = 1234567890123  # 模拟微秒时间戳
    
    # 转换为毫秒
    current_time_ms = esp_timer_get_time // 1000
    
    print(f"esp_timer_get_time (微秒): {esp_timer_get_time}")
    print(f"转换为毫秒: {current_time_ms}")
    print(f"类型: {type(current_time_ms)}")
    
    # 测试不同的格式符
    print("\n=== 格式符测试 ===")
    
    # 正确的格式符（对于uint32_t）
    print(f"uint32_t 使用 %lu: {current_time_ms}")
    
    # 错误的格式符（会导致编译错误）
    print("注意: 如果直接使用 esp_timer_get_time() / 1000 而不转换类型，")
    print("应该使用 %lld 而不是 %lu")
    
    print("\n=== 修复建议 ===")
    print("1. 将 esp_timer_get_time() / 1000 的结果存储为 uint32_t 变量")
    print("2. 使用 %lu 格式符打印 uint32_t 变量")
    print("3. 或者直接使用 %lld 格式符打印 int64_t 结果")

if __name__ == "__main__":
    test_format_specifiers() 