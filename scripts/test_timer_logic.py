#!/usr/bin/env python3
"""
测试定时器逻辑的Python脚本
模拟ESP32的任务检查定时器逻辑
"""

import time
import threading
from datetime import datetime

class TaskCheckTimer:
    def __init__(self, interval_ms=5000):
        self.interval_ms = interval_ms
        self.task_check_in_progress = False
        self.timer_thread = None
        self.running = False
        self.last_check_time = None
        
    def start(self):
        """启动定时器"""
        self.running = True
        self.timer_thread = threading.Thread(target=self._timer_loop)
        self.timer_thread.daemon = True
        self.timer_thread.start()
        print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] Timer started with {self.interval_ms}ms interval")
        
    def stop(self):
        """停止定时器"""
        self.running = False
        if self.timer_thread:
            self.timer_thread.join()
            
    def _timer_loop(self):
        """定时器循环"""
        while self.running:
            time.sleep(self.interval_ms / 1000.0)
            if self.running:
                self._on_timer_triggered()
                
    def _on_timer_triggered(self):
        """定时器触发回调"""
        current_time = datetime.now()
        print(f"[{current_time.strftime('%H:%M:%S.%f')[:-3]}] Timer triggered, task_check_in_progress={self.task_check_in_progress}")
        
        if not self.task_check_in_progress:
            print(f"[{current_time.strftime('%H:%M:%S.%f')[:-3]}] Setting task check event")
            self._check_device_task()
        else:
            print(f"[{current_time.strftime('%H:%M:%S.%f')[:-3]}] Task check already in progress, skipping")
            
    def _check_device_task(self):
        """模拟任务检查"""
        current_time = datetime.now()
        self.last_check_time = current_time
        print(f"[{current_time.strftime('%H:%M:%S.%f')[:-3]}] CheckDeviceTask started")
        
        # 设置处理中标志
        self.task_check_in_progress = True
        print(f"[{current_time.strftime('%H:%M:%S.%f')[:-3]}] Set task_check_in_progress=True")
        
        # 模拟任务处理时间（随机1-3秒）
        import random
        processing_time = random.uniform(1.0, 3.0)
        print(f"[{current_time.strftime('%H:%M:%S.%f')[:-3]}] Simulating task processing for {processing_time:.1f}s")
        time.sleep(processing_time)
        
        # 任务处理完成
        completion_time = datetime.now()
        print(f"[{completion_time.strftime('%H:%M:%S.%f')[:-3]}] Task processing completed")
        
        # 重新启动定时器
        self._restart_timer()
        
    def _restart_timer(self):
        """重新启动定时器"""
        current_time = datetime.now()
        print(f"[{current_time.strftime('%H:%M:%S.%f')[:-3]}] RestartTaskCheckTimer started")
        
        # 清除处理中标志
        self.task_check_in_progress = False
        print(f"[{current_time.strftime('%H:%M:%S.%f')[:-3]}] Set task_check_in_progress=False")
        
        # 重新启动定时器（这里不需要实际重启，因为定时器是连续的）
        print(f"[{current_time.strftime('%H:%M:%S.%f')[:-3]}] Timer will trigger in {self.interval_ms}ms")
        print(f"[{current_time.strftime('%H:%M:%S.%f')[:-3]}] RestartTaskCheckTimer completed")

def main():
    """主函数"""
    print("=== 任务检查定时器逻辑测试 ===")
    print("模拟ESP32的任务检查定时器，验证5秒间隔逻辑")
    print()
    
    # 创建定时器
    timer = TaskCheckTimer(interval_ms=5000)
    
    try:
        # 启动定时器
        timer.start()
        
        # 运行30秒
        print("运行30秒进行测试...")
        time.sleep(30)
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    finally:
        # 停止定时器
        timer.stop()
        print("测试完成")

if __name__ == "__main__":
    main() 