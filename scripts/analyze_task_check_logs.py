#!/usr/bin/env python3
"""
任务检查日志分析脚本

这个脚本用于分析ESP32设备任务检查功能的详细日志，帮助调试和优化。
"""

import re
import sys
from datetime import datetime
from collections import defaultdict, Counter

class TaskCheckLogAnalyzer:
    def __init__(self):
        self.logs = []
        self.analysis = {
            'total_checks': 0,
            'successful_checks': 0,
            'failed_checks': 0,
            'tasks_received': 0,
            'custom_messages_sent': 0,
            'audio_channel_opens': 0,
            'audio_channel_open_failures': 0,
            'http_errors': 0,
            'json_parse_errors': 0,
            'retry_attempts': 0,
            'timing_stats': defaultdict(list)
        }
        
    def parse_log_line(self, line):
        """解析单行日志"""
        # ESP32日志格式: I (timestamp) TAG: message
        pattern = r'([IWED]) \((\d+)\) (\w+): (.+)'
        match = re.match(pattern, line.strip())
        
        if match:
            level, timestamp, tag, message = match.groups()
            return {
                'level': level,
                'timestamp': int(timestamp),
                'tag': tag,
                'message': message,
                'raw': line.strip()
            }
        return None
    
    def analyze_logs(self, log_file):
        """分析日志文件"""
        print(f"正在分析日志文件: {log_file}")
        print("-" * 60)
        
        with open(log_file, 'r', encoding='utf-8') as f:
            for line in f:
                log_entry = self.parse_log_line(line)
                if log_entry:
                    self.logs.append(log_entry)
                    self.analyze_log_entry(log_entry)
        
        self.print_analysis()
    
    def analyze_log_entry(self, entry):
        """分析单个日志条目"""
        message = entry['message']
        level = entry['level']
        timestamp = entry['timestamp']
        
        # 统计任务检查次数
        if 'CheckDeviceTask: Method started' in message:
            self.analysis['total_checks'] += 1
            self.analysis['timing_stats']['check_start'].append(timestamp)
            
        elif 'CheckDeviceTask: Method completed' in message:
            self.analysis['timing_stats']['check_end'].append(timestamp)
            
        elif 'CheckDeviceTask: Device task received' in message:
            self.analysis['tasks_received'] += 1
            
        elif 'CheckDeviceTask: TASK_CHECK_SEND_CUSTOM_MESSAGE is enabled' in message:
            self.analysis['custom_messages_sent'] += 1
            
        elif 'SendCustomMessage: Audio channel not opened, attempting to open' in message:
            self.analysis['audio_channel_opens'] += 1
            
        elif 'SendCustomMessage: Audio channel opened successfully' in message:
            self.analysis['timing_stats']['audio_open_success'].append(timestamp)
            
        elif 'SendCustomMessage: Failed to open audio channel' in message:
            self.analysis['audio_channel_open_failures'] += 1
            
        elif 'CheckDeviceTask: Failed to open HTTP connection' in message:
            self.analysis['http_errors'] += 1
            
        elif 'CheckDeviceTask: Failed to parse task check response' in message:
            self.analysis['json_parse_errors'] += 1
            
        elif 'CheckDeviceTask: Attempt' in message and 'retry' in message.lower():
            self.analysis['retry_attempts'] += 1
            
        elif 'CheckDeviceTask: HTTP status code: 200' in message:
            self.analysis['successful_checks'] += 1
            
        elif 'CheckDeviceTask: Task check failed, status code:' in message:
            self.analysis['failed_checks'] += 1
    
    def print_analysis(self):
        """打印分析结果"""
        print("\n" + "=" * 60)
        print("任务检查功能日志分析报告")
        print("=" * 60)
        
        # 基本统计
        print(f"\n📊 基本统计:")
        print(f"  总检查次数: {self.analysis['total_checks']}")
        print(f"  成功检查: {self.analysis['successful_checks']}")
        print(f"  失败检查: {self.analysis['failed_checks']}")
        if self.analysis['total_checks'] > 0:
            success_rate = (self.analysis['successful_checks'] / self.analysis['total_checks']) * 100
            print(f"  成功率: {success_rate:.1f}%")
        
        # 任务相关
        print(f"\n📋 任务相关:")
        print(f"  收到任务数: {self.analysis['tasks_received']}")
        print(f"  发送custom消息数: {self.analysis['custom_messages_sent']}")
        
        # 音频通道
        print(f"\n🔊 音频通道:")
        print(f"  尝试打开音频通道: {self.analysis['audio_channel_opens']}")
        print(f"  音频通道打开失败: {self.analysis['audio_channel_open_failures']}")
        if self.analysis['audio_channel_opens'] > 0:
            open_success_rate = ((self.analysis['audio_channel_opens'] - self.analysis['audio_channel_open_failures']) / self.analysis['audio_channel_opens']) * 100
            print(f"  音频通道打开成功率: {open_success_rate:.1f}%")
        
        # 错误统计
        print(f"\n❌ 错误统计:")
        print(f"  HTTP连接错误: {self.analysis['http_errors']}")
        print(f"  JSON解析错误: {self.analysis['json_parse_errors']}")
        print(f"  重试次数: {self.analysis['retry_attempts']}")
        
        # 时间分析
        self.print_timing_analysis()
        
        # 详细日志示例
        self.print_log_examples()
    
    def print_timing_analysis(self):
        """打印时间分析"""
        print(f"\n⏱️  时间分析:")
        
        if len(self.analysis['timing_stats']['check_start']) > 1:
            intervals = []
            for i in range(1, len(self.analysis['timing_stats']['check_start'])):
                interval = self.analysis['timing_stats']['check_start'][i] - self.analysis['timing_stats']['check_start'][i-1]
                intervals.append(interval)
            
            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                print(f"  平均检查间隔: {avg_interval:.0f}ms")
                print(f"  最小检查间隔: {min(intervals)}ms")
                print(f"  最大检查间隔: {max(intervals)}ms")
        
        if self.analysis['timing_stats']['audio_open_success']:
            print(f"  音频通道成功打开次数: {len(self.analysis['timing_stats']['audio_open_success'])}")
    
    def print_log_examples(self):
        """打印关键日志示例"""
        print(f"\n📝 关键日志示例:")
        
        # 查找任务接收的日志
        task_logs = [log for log in self.logs if 'CheckDeviceTask: Device task received' in log['message']]
        if task_logs:
            print(f"\n  任务接收示例:")
            for log in task_logs[:2]:  # 显示前2个
                print(f"    {log['raw']}")
        
        # 查找custom消息发送的日志
        custom_logs = [log for log in self.logs if 'SendCustomMessage: Custom message sent successfully' in log['message']]
        if custom_logs:
            print(f"\n  Custom消息发送示例:")
            for log in custom_logs[:2]:  # 显示前2个
                print(f"    {log['raw']}")
        
        # 查找错误日志
        error_logs = [log for log in self.logs if log['level'] == 'E']
        if error_logs:
            print(f"\n  错误日志示例:")
            for log in error_logs[:3]:  # 显示前3个
                print(f"    {log['raw']}")
    
    def search_pattern(self, pattern):
        """搜索特定模式的日志"""
        matching_logs = []
        for log in self.logs:
            if re.search(pattern, log['message'], re.IGNORECASE):
                matching_logs.append(log)
        return matching_logs

def main():
    if len(sys.argv) < 2:
        print("用法: python3 analyze_task_check_logs.py <log_file>")
        print("示例: python3 analyze_task_check_logs.py esp32_logs.txt")
        sys.exit(1)
    
    log_file = sys.argv[1]
    
    try:
        analyzer = TaskCheckLogAnalyzer()
        analyzer.analyze_logs(log_file)
    except FileNotFoundError:
        print(f"错误: 找不到日志文件 {log_file}")
        sys.exit(1)
    except Exception as e:
        print(f"分析过程中出错: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main() 