#!/usr/bin/env python3
"""
Custom消息自动连接测试脚本

这个脚本用于测试ESP32设备在音频通道未打开时自动建立连接并发送custom消息的功能。
"""

import json
import time
import asyncio
import websockets
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AutoConnectTestHandler:
    def __init__(self):
        self.connection_count = 0
        self.custom_messages = []
        self.hello_messages = []
    
    def handle_message(self, message):
        """处理接收到的消息"""
        try:
            data = json.loads(message)
            message_type = data.get('type', 'unknown')
            
            logger.info(f"收到消息类型: {message_type}")
            logger.info(f"消息内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            if message_type == 'hello':
                logger.info("收到hello消息，音频通道已建立")
                self.hello_messages.append(data)
                self.connection_count += 1
                logger.info(f"这是第 {self.connection_count} 次建立连接")
                
                # 发送服务器hello响应
                return self.create_server_hello_response()
            
            elif message_type == 'custom':
                logger.info("收到custom消息！")
                self.custom_messages.append(data)
                
                payload = data.get('payload', {})
                task_content = payload.get('task_content', '')
                
                logger.info(f"任务内容: {task_content}")
                logger.info(f"连接次数: {self.connection_count}")
                
                # 验证是否在正确的连接状态下收到custom消息
                if self.connection_count > 0:
                    logger.info("✅ 验证通过: custom消息在音频通道建立后发送")
                else:
                    logger.warning("⚠️  警告: custom消息在音频通道建立前发送")
                
                return self.create_ack_response(task_content)
            
            else:
                logger.info(f"收到其他类型消息: {message_type}")
                return None
                
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
            return None
        except Exception as e:
            logger.error(f"处理消息时出错: {e}")
            return None
    
    def create_server_hello_response(self):
        """创建服务器hello响应"""
        response = {
            "type": "hello",
            "session_id": f"session_{int(time.time())}",
            "transport": "websocket",
            "audio_params": {
                "sample_rate": 16000,
                "frame_duration": 60
            }
        }
        return json.dumps(response)
    
    def create_ack_response(self, task_content):
        """创建任务确认响应"""
        response = {
            "type": "custom_ack",
            "task_content": task_content,
            "status": "received",
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime())
        }
        return json.dumps(response)
    
    def print_summary(self):
        """打印测试总结"""
        logger.info("=" * 50)
        logger.info("测试总结:")
        logger.info(f"总连接次数: {self.connection_count}")
        logger.info(f"Hello消息数量: {len(self.hello_messages)}")
        logger.info(f"Custom消息数量: {len(self.custom_messages)}")
        
        if self.custom_messages:
            logger.info("✅ 测试成功: 设备能够自动建立连接并发送custom消息")
        else:
            logger.warning("⚠️  测试失败: 未收到custom消息")
        
        logger.info("=" * 50)

# WebSocket服务器处理
async def websocket_handler(websocket, path):
    """WebSocket连接处理"""
    handler = AutoConnectTestHandler()
    client_id = f"client_{int(time.time())}"
    
    logger.info(f"新的WebSocket连接: {client_id}")
    
    try:
        async for message in websocket:
            logger.info(f"收到来自 {client_id} 的消息: {message}")
            
            # 处理消息
            response = handler.handle_message(message)
            
            if response:
                await websocket.send(response)
                logger.info(f"发送响应给 {client_id}: {response}")
    
    except websockets.exceptions.ConnectionClosed:
        logger.info(f"WebSocket连接关闭: {client_id}")
        handler.print_summary()
    except Exception as e:
        logger.error(f"WebSocket处理错误: {e}")

async def start_websocket_server(host='localhost', port=8081):
    """启动WebSocket服务器"""
    server = await websockets.serve(websocket_handler, host, port)
    logger.info(f"WebSocket服务器启动在 ws://{host}:{port}")
    logger.info("等待ESP32设备连接...")
    logger.info("此服务器用于测试自动连接功能")
    logger.info("-" * 50)
    
    await server.wait_closed()

def start_websocket_server_sync(host='localhost', port=8081):
    """同步启动WebSocket服务器"""
    try:
        asyncio.run(start_websocket_server(host, port))
    except KeyboardInterrupt:
        logger.info("服务器停止")

if __name__ == '__main__':
    import sys
    
    # 解析命令行参数
    host = 'localhost'
    port = 8081
    
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("端口号必须是整数")
            sys.exit(1)
    
    if len(sys.argv) > 2:
        host = sys.argv[2]
    
    print("Custom消息自动连接测试服务器")
    print(f"监听地址: ws://{host}:{port}")
    print("按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    start_websocket_server_sync(host, port) 